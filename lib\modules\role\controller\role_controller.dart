import 'package:get/get.dart';
import 'package:rolio/common/models/ai_role.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/modules/role/service/role_service.dart';
import 'package:rolio/common/utils/image_preloader.dart';
import 'package:rolio/common/utils/toast_util.dart';
import 'dart:async';  // 导入用于StreamSubscription

/// 角色控制器
/// 负责管理角色详情页的UI状态和业务逻辑
class RoleController extends GetxController {
  // 服务
  late final RoleService roleService;
  
  // 当前角色
  final Rx<AiRole?> currentRole = Rx<AiRole?>(null);
  
  // 加载状态
  final RxBool isLoading = true.obs;
  
  // 收藏操作状态
  final RxBool isFavoriteLoading = false.obs;
  
  // 错误状态
  final RxBool hasError = false.obs;
  final RxString errorMessage = ''.obs;
  
  // 图片预加载状态
  final RxBool isCoverPreloaded = false.obs;
  
  // 图片预加载器
  ImagePreloader get _imagePreloader => Get.find<ImagePreloader>();

  // 跟踪当前预加载的图片URL
  final Set<String> _preloadingImages = <String>{};
  // 预加载重试次数记录
  final Map<String, int> _preloadRetryCount = <String, int>{};
  // 最大预加载重试次数
  static const int MAX_PRELOAD_RETRY = 2;

  // 重试次数
  int _retryCount = 0;
  static const int MAX_RETRY = 3;
  
  @override
  void onInit() {
    super.onInit();
    
    // 获取服务实例
    roleService = Get.find<RoleService>();
    
    // 获取路由参数
    final arguments = Get.arguments;
    if (arguments != null && arguments is Map<String, dynamic>) {
      final roleId = arguments['roleId'];
      if (roleId != null && roleId is int) {
        // 获取角色详情（包含收藏信息）
        getRoleDetail(roleId);
      } else {
        LogUtil.error('角色ID无效: $roleId');
        _setError('无效的角色ID');
      }
    } else {
      LogUtil.error('未提供角色ID参数');
      _setError('未提供角色ID');
    }
  }
  
  /// 打开搜索页面
  void goToSearch() {
    LogUtil.debug('打开搜索页面');
    Get.toNamed('/role/search');
  }

  /// 获取角色详情
  /// 
  /// 通过RoleService获取角色详细信息
  /// [roleId] 角色ID
  /// [forceRefresh] 是否强制刷新，不使用缓存
  Future<void> getRoleDetail(int roleId, {bool forceRefresh = false}) async {
    try {
      isLoading.value = true;
      hasError.value = false;
      
      LogUtil.info('获取角色详情: ID=$roleId, forceRefresh=$forceRefresh');
      
      // 从RoleService获取完整角色详情
      final role = await roleService.getRoleDetail(roleId, forceRefresh: forceRefresh);
      
      if (role != null) {
        // 更新当前角色
        currentRole.value = role;
        _retryCount = 0; // 成功后重置重试次数
        LogUtil.debug('成功获取角色详情: ${role.name}, 收藏状态: ${role.isFavorited}');
        
        // 检查封面图是否已预加载
        _checkAndPreloadCoverImage(role);
      } else {
        LogUtil.error('未找到角色: ID=$roleId');
        _setError('Role not found');
      }
    } catch (e) {
      LogUtil.error('获取角色详情失败: $e');
      _setError('Failed to load role details');
      
      // 自动重试逻辑 - 仅在非强制刷新且重试次数未超过上限时
      if (!forceRefresh && _retryCount < MAX_RETRY) {
        _retryCount++;
        LogUtil.info('自动重试获取角色详情，第$_retryCount次: ID=$roleId');
        
        // 延迟1秒后重试
        Future.delayed(const Duration(seconds: 1), () {
          getRoleDetail(roleId, forceRefresh: true);
        });
      } else {
        ErrorHandler.handleException(e);
      }
    } finally {
      isLoading.value = false;
    }
  }
  
  /// 重试获取角色详情
  Future<void> retryGetRoleDetail() async {
    final role = currentRole.value;
    if (role != null) {
      // 重试获取角色详情
      _retryCount++;
      LogUtil.info('重试获取角色详情，第$_retryCount次: ID=${role.id}');
      await getRoleDetail(role.id, forceRefresh: true);
    } else {
      // 从路由参数获取角色ID
      final arguments = Get.arguments;
      if (arguments != null && arguments is Map<String, dynamic>) {
        final roleId = arguments['roleId'];
        if (roleId != null && roleId is int) {
          _retryCount++;
          LogUtil.info('从路由参数重试获取角色详情，第$_retryCount次: ID=$roleId');
          await getRoleDetail(roleId, forceRefresh: true);
        } else {
          // 如果没有角色ID，无法重试
          LogUtil.error('无法重试，当前没有角色ID');
          ToastUtil.error('Cannot retry, no role ID available');
        }
      } else {
        // 如果没有角色ID，无法重试
        LogUtil.error('无法重试，当前没有角色ID');
        ToastUtil.error('Cannot retry, no role ID available');
      }
    }
  }

  /// 切换收藏状态
  ///
  /// 如果角色已收藏则取消收藏，否则收藏角色
  Future<void> toggleFavorite() async {
    try {
      final role = currentRole.value;
      if (role == null) {
        LogUtil.warn('当前没有角色，无法切换收藏状态');
        return;
      }

      isFavoriteLoading.value = true;
      LogUtil.debug('切换收藏状态，当前状态: ${role.isFavorited}, 角色ID: ${role.id}');

      // 调用服务层统一方法切换收藏状态
      final newState = await roleService.toggleFavorite(role.id);

      if (newState == null) {
        // 操作失败，显示错误信息
        ToastUtil.error('Failed to change favorite status');
      } else {
        // 操作成功，显示成功提示
        if (newState) {
          ToastUtil.success('Added to favorites');
        } else {
          ToastUtil.success('Removed from favorites');
        }

        // 服务层已经更新了状态，这里确保UI状态一致
        if (currentRole.value != null) {
          currentRole.value = currentRole.value!.copyWith(
            isFavorited: newState,
            favoritedAt: newState ? DateTime.now() : null,
          );
        }
      }
    } catch (e) {
      LogUtil.error('切换收藏状态异常: $e');
      ToastUtil.error('An error occurred while updating favorite status');
      ErrorHandler.handleException(e);
    } finally {
      isFavoriteLoading.value = false;
    }
  }
  
  /// 检查并预加载封面图
  void _checkAndPreloadCoverImage(AiRole role) {
    if (role.coverUrl.isEmpty) {
      isCoverPreloaded.value = true;
      return;
    }
    
    // 检查图片是否已预加载
    isCoverPreloaded.value = _imagePreloader.isImagePreloaded(role.coverUrl);
    
    if (!isCoverPreloaded.value) {
      LogUtil.debug('封面图未预加载，开始预加载: ${role.coverUrl}');
      
      // 记录正在预加载的URL
      _preloadingImages.add(role.coverUrl);
      
      // 高优先级预加载封面图
      _imagePreloader.preloadImage(
        role.coverUrl,
        priority: ImagePreloadPriority.high,
        onComplete: (success) {
          LogUtil.debug('封面图预加载${success ? "成功" : "失败"}: ${role.coverUrl}');
          
          // 从预加载集合中移除
          _preloadingImages.remove(role.coverUrl);
          
          if (success) {
            isCoverPreloaded.value = true;
            // 重置重试计数
            _preloadRetryCount.remove(role.coverUrl);
          } else {
            // 预加载失败，尝试重试
            _handlePreloadFailure(role.coverUrl, isHighPriority: true);
          }
        }
      );
    } else {
      LogUtil.debug('封面图已预加载: ${role.coverUrl}');
    }
    
    // 预加载头像图片
    if (role.avatarUrl.isNotEmpty && !_imagePreloader.isImagePreloaded(role.avatarUrl)) {
      // 记录正在预加载的URL
      _preloadingImages.add(role.avatarUrl);
      
      _imagePreloader.preloadImage(
        role.avatarUrl,
        priority: ImagePreloadPriority.medium,
        onComplete: (success) {
          // 从预加载集合中移除
          _preloadingImages.remove(role.avatarUrl);
          
          if (!success) {
            // 预加载失败，尝试重试
            _handlePreloadFailure(role.avatarUrl, isHighPriority: false);
          }
        }
      );
    }
  }
  
  /// 处理预加载失败的情况
  void _handlePreloadFailure(String imageUrl, {bool isHighPriority = false}) {
    // 获取当前重试次数，默认为0
    final retryCount = _preloadRetryCount[imageUrl] ?? 0;
    
    // 检查是否超过最大重试次数
    if (retryCount < MAX_PRELOAD_RETRY) {
      // 增加重试计数
      _preloadRetryCount[imageUrl] = retryCount + 1;
      
      LogUtil.debug('图片预加载失败，准备第${retryCount + 1}次重试: $imageUrl');
      
      // 延迟一段时间后重试
      Future.delayed(Duration(milliseconds: 500 * (retryCount + 1)), () {
        // 确保控制器仍然存活
        if (Get.isRegistered<RoleController>()) {
          // 记录正在预加载的URL
          _preloadingImages.add(imageUrl);
          
          // 重新尝试预加载
          _imagePreloader.preloadImage(
            imageUrl,
            priority: isHighPriority ? ImagePreloadPriority.high : ImagePreloadPriority.medium,
            onComplete: (success) {
              // 从预加载集合中移除
              _preloadingImages.remove(imageUrl);
              
              if (success) {
                LogUtil.debug('图片预加载重试成功: $imageUrl');
                // 如果是封面图，更新状态
                if (currentRole.value != null && currentRole.value!.coverUrl == imageUrl) {
                  isCoverPreloaded.value = true;
                }
                // 重置重试计数
                _preloadRetryCount.remove(imageUrl);
              } else if (_preloadRetryCount[imageUrl]! < MAX_PRELOAD_RETRY) {
                // 继续重试
                _handlePreloadFailure(imageUrl, isHighPriority: isHighPriority);
              } else {
                LogUtil.warn('图片预加载失败，已达到最大重试次数: $imageUrl');
              }
            }
          );
        }
      });
    } else {
      LogUtil.warn('图片预加载失败，已达到最大重试次数: $imageUrl');
      _preloadRetryCount.remove(imageUrl);
    }
  }
  
  /// 设置错误状态
  void _setError(String message) {
    hasError.value = true;
    errorMessage.value = message;
  }
  
  @override
  void onClose() {
    // 清理图片预加载器资源
    if (currentRole.value != null) {
      final role = currentRole.value!;
      
      // 清理特定角色的预加载资源
      if (role.coverUrl.isNotEmpty) {
        LogUtil.debug('清理封面图预加载资源: ${role.coverUrl}');
        // 从预加载集合中移除
        _preloadingImages.remove(role.coverUrl);
        _preloadRetryCount.remove(role.coverUrl);
      }
      
      if (role.avatarUrl.isNotEmpty) {
        LogUtil.debug('清理头像图预加载资源: ${role.avatarUrl}');
        // 从预加载集合中移除
        _preloadingImages.remove(role.avatarUrl);
        _preloadRetryCount.remove(role.avatarUrl);
      }
    }
    
    // 清理所有正在预加载的资源
    if (_preloadingImages.isNotEmpty) {
      LogUtil.debug('清理剩余${_preloadingImages.length}个预加载图片资源');
      _preloadingImages.clear();
    }
    
    // 清理重试计数
    _preloadRetryCount.clear();
    
    // 释放图片预加载器资源
    // 使用dispose方法替代clearMemoryCache，确保完全释放资源
    _imagePreloader.dispose();
    
    // 释放其他资源
    _retryCount = 0;
    currentRole.value = null;
    
    LogUtil.debug('RoleController: 已释放资源');
    super.onClose();
  }
}
